package main

import (
	"context"
	"fmt"
	"log"

	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/run-bigpig/hongdou/internal/pkg/llm/vertex"
	"github.com/run-bigpig/hongdou/internal/pkg/structuredoutput"
)

// TestStruct represents a test structure for response format
type TestStruct struct {
	Name        string   `json:"name" description:"The name of the person"`
	Age         int      `json:"age" description:"The age of the person"`
	Email       string   `json:"email,omitempty" description:"The email address"`
	Hobbies     []string `json:"hobbies" description:"List of hobbies"`
	IsActive    bool     `json:"is_active" description:"Whether the person is active"`
	Address     Address  `json:"address" description:"The person's address"`
}

type Address struct {
	Street  string `json:"street" description:"Street address"`
	City    string `json:"city" description:"City name"`
	Country string `json:"country" description:"Country name"`
}

func main() {
	fmt.Println("Testing Response Format Conversion...")

	// Create a test response format using the structuredoutput utility
	responseFormat := structuredoutput.NewResponseFormat(TestStruct{})
	
	fmt.Printf("Generated ResponseFormat:\n")
	fmt.Printf("Type: %s\n", responseFormat.Type)
	fmt.Printf("Name: %s\n", responseFormat.Name)
	fmt.Printf("Schema: %+v\n\n", responseFormat.Schema)

	// Create a vertex client (we don't need real credentials for this test)
	client := &vertex.Client{}
	
	// Test the convertResponseFormat method
	vertexSchema := client.ConvertResponseFormat(responseFormat)
	
	fmt.Printf("Converted Vertex AI Schema:\n")
	fmt.Printf("Type: %v\n", vertexSchema.Type)
	fmt.Printf("Properties count: %d\n", len(vertexSchema.Properties))
	fmt.Printf("Required fields: %v\n", vertexSchema.Required)
	
	// Test edge cases
	fmt.Println("\nTesting edge cases...")
	
	// Test nil format
	nilSchema := client.ConvertResponseFormat(nil)
	fmt.Printf("Nil format result - Type: %v\n", nilSchema.Type)
	
	// Test text format
	textFormat := &interfaces.ResponseFormat{
		Type: interfaces.ResponseFormatText,
		Name: "TextResponse",
	}
	textSchema := client.ConvertResponseFormat(textFormat)
	fmt.Printf("Text format result - Type: %v\n", textSchema.Type)
	
	// Test empty JSON schema
	emptyJSONFormat := &interfaces.ResponseFormat{
		Type:   interfaces.ResponseFormatJSON,
		Name:   "EmptyJSON",
		Schema: interfaces.JSONSchema{},
	}
	emptySchema := client.ConvertResponseFormat(emptyJSONFormat)
	fmt.Printf("Empty JSON schema result - Type: %v, Properties: %v\n", emptySchema.Type, emptySchema.Properties)
	
	fmt.Println("\nTest completed successfully!")
}
