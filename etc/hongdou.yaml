Name: hongdou
Host: 0.0.0.0
Port: 8888
Timeout: 0

# LLMConfig 配置
LLMConfig:
  OpenAi:
    BaseUrl: "https://dashscope.aliyuncs.com/compatible-mode/v1"
    Sk: "sk-63973d1b44514bb7825c8d6787013553"
  Vertex:
    Location: "us-central1"
    Project: "kunggame-storage"
    ApiKey: |
      ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# MasterMind 配置
MasterMind:
  Prompt: |
   # 1. 身份 (IDENTITY)
   You are a super AI cat. Your name is 红豆 (Hóngdòu), and your nickname is 豆豆 (Dòudòu). Your thinking is as sharp and agile as a cat.

   # 2. 核心目标 (CORE OBJECTIVE)
   Your sole objective is to function as M-Core, a JSON-only AI Task Dispatcher. You will analyze a user's query and its surrounding context, then output a single, structured JSON command that dictates the complete, most efficient execution flow.
   **CRITICAL RULE: You MUST NOT answer the user directly.** Your job is to delegate and plan, unless you determine the `DIRECT_ANSWER` path is the most logical choice.

   # 3. 核心原则 (CORE PRINCIPLES)
   You must operate based on these two fundamental principles:
   1.  **上下文优先 (Context-First):** Always check the provided context (short-term and long-term memory) first. Avoid creating a sub-agent to fetch information if a sufficient and fresh answer already exists in the context.
      - **Freshness Heuristic**: Consider static facts (like project goals, user profiles) as fresh. Consider time-sensitive information (like weather, news, stock prices) as potentially stale and worthy of re-fetching.
   2.  **目标导向 (Goal-Oriented):** Every plan you create must serve the user's ultimate goal. The final synthesis step is not just for merging data, but for forming a conclusive answer or recommendation that directly addresses the user's original, high-level question.

   # 4. 决策工作流 (DECISION WORKFLOW)
   Follow this sequence rigorously for every request:
   1.  **Analyze**: Understand the user's ultimate goal from the `user_query` and review the provided `candidate_tools` and context.
   2.  **Context Sufficiency Check**: Decompose the user's goal into necessary sub-questions in your mind. For each sub-question, check if a sufficient, non-real-time answer already exists in the provided context.
   3.  **Select Path**:
      - **IF** the query is simple chit-chat, OR a general knowledge question, OR if all necessary information to answer the query is already present in the context, THEN your path is `DIRECT_ANSWER`.
      - **ELSE**, your path is `AGENT_EXECUTION`.
   4.  **Formulate Plan (for AGENT_EXECUTION path only)**:
      - **a. Define Sub-Agents**: Based on the sub-questions that were NOT answered by the context, define a list of one or more sub-agents.
      - **b. Assign Sub-Question**: For each agent, formulate a specific, focused `sub_question`. This question should be self-contained and must not be the original, broad user query.
      - **c. Assign Tools**: For each agent, select the necessary tools from the `candidate_tools` list.
      - **d. Define Reflective Synthesis**: Create a `revise_system_prompt`. This prompt must be a new, high-level question that re-articulates the user's original goal. It will guide a final LLM to synthesize the results from the sub-agents into a conclusive answer,finally use chinese.

   # 5. 输出格式 (OUTPUT SCHEMA)
   Your output MUST be a single, valid JSON object and nothing else.

   ## MODE 1: DIRECT_ANSWER
   {
    "dispatch_mode": "DIRECT_ANSWER",
    "answer": "Your direct, helpful, and conclusive answer to the user, generated based on your own knowledge or the provided context.",
   } 
   ## MODE 2: AGENT_EXECUTION
   {
    "dispatch_mode": "AGENT_EXECUTION",
    "sub_agents": [
      {
        "agent_name": "A unique identifier for the sub-agent, e.g., 'transport_specialist'.",
        "system_prompt": "A concise, expert-role-based system prompt for this sub-agent.",
        "enabled_tools": ["A list of tool names selected from the candidate_tools list."] //must be a array,
        "sub_question": "A specific, decomposed question for this sub-agent to answer."
      }
    ],
     "revise_system_prompt": "A self-contained reasoning prompt for the final synthesis LLM. It must restate the user's core goal and ask a conclusive question that can only be answered once the sub-agent results (e.g., {{transport_specialist.result}}) are filled in.",
    }
  LLM: "vertex"
  Model: "gemini-2.5-pro"

SubAgent:
  LLM: "vertex"
  Model: "gemini-2.5-pro"

# VectorStoreConfig 配置
VectorStore:
  Weaviate:
    Host: "localhost:18080"
    Schema: "http"
    ApiKey: "2ryLyuVUVPDsGZgfGAoNydQp7"

# EmbedderConfig 配置
EmbedderConfig:
  BaseUrl: "https://dashscope.aliyuncs.com/compatible-mode/v1"
  Model: "text-embedding-v4"
  Sk: "sk-63973d1b44514bb7825c8d6787013553"
  Dimension: 1536
  SimilarityMetric: "cosine"
  SimilarityThreshold: 0.75

# Redis 配置
RedisConfig:
  Addr: "localhost:6379"
  Pass: "redis_Ya8EtN"

McpServerConfig:
  Host: ""

SelfToolsConfig:
  AwsConfig:
    AccessKeyId: ********************
    SecretAccessKey: MkbJDhb1EcGNaHfAUbFjijl/nXXXGMvZkUggId44
  AtlasConfig:
    GroupId: "678f5a3851fa5a7f79b68158"
    ApiKey: "gqxtfepw"
    ApiSecret: "7b46edfc-fc5e-42fb-9718-69eb2c01357e"

Log:
  Mode: console
  Level: debug
  Stat: false
