package tools

import (
	"context"
	"game/api/mcp/internal/svc"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/costexplorer"
	"github.com/aws/aws-sdk-go-v2/service/costexplorer/types"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/mcp"
	"log"
	"time"
)

type AwsBillingTool struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logger logx.Logger
	tool   mcp.Tool
}

func NewAwsBillingTool(ctx context.Context, svcCtx *svc.ServiceContext, mcpServer mcp.McpServer) {
	t := &AwsBillingTool{
		ctx:    ctx,
		svcCtx: svcCtx,
		logger: logx.WithContext(ctx),
		tool: mcp.Tool{
			Name:        "get_aws_bill",
			Description: "Get the billing details of Amazon AWS cloud service within the specified date range. This tool is suitable for users to inquire about AWS fees, costs or usage in a specific period of time. It can provide detailed billing information to help users analyze and manage the overhead of cloud resources.",
		},
	}
	t.SetInputSchema()
	t.SetHandler()
	err := mcpServer.RegisterTool(t.tool)
	if err != nil {
		log.Fatalf("Error registering tool: %v", err)
	}
}

func (tool *AwsBillingTool) SetInputSchema() {
	tool.tool.InputSchema = mcp.InputSchema{
		Properties: map[string]any{
			"start": map[string]any{
				"type":        "string",
				"description": "The start date of bill inquiry, formatted as YYYY-MM-DD. For example,' 2023-01-01'.",
			},
			"end": map[string]any{
				"type":        "string",
				"description": "The end date of bill inquiry, formatted as YYYY-MM-DD. For example,' 2023-01-31'. Must be later than the start date.",
			},
		},
		Required: []string{"start", "end"},
	}
}

func (tool *AwsBillingTool) SetHandler() {
	tool.tool.Handler = func(ctx context.Context, params map[string]any) (any, error) {
		var req struct {
			Start string `json:"start"`
			End   string `json:"end"`
		}
		if err := mcp.ParseArguments(params, &req); err != nil {
			return nil, err
		}
		//结束日期加一天
		req.End = tool.addDay(req.End, 1)
		//获取aws配置
		conf, err := config.LoadDefaultConfig(context.TODO(),
			config.WithRegion("us-east-1"),
			config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(tool.svcCtx.Config.AwsConf.AccessKeyId, tool.svcCtx.Config.AwsConf.SecretAccessKey, "")),
		)
		if err != nil {
			tool.logger.Errorf("Error loading AWS config: %v", err)
			return nil, err
		}
		client := costexplorer.NewFromConfig(conf)
		resp, err := client.GetCostAndUsage(context.TODO(), &costexplorer.GetCostAndUsageInput{
			Granularity: "MONTHLY",
			GroupBy: []types.GroupDefinition{
				{
					Type: types.GroupDefinitionTypeDimension,
					Key:  aws.String("SERVICE"),
				},
			},
			TimePeriod: &types.DateInterval{
				Start: aws.String(req.Start),
				End:   aws.String(req.End),
			},
			Metrics: []string{"BlendedCost"},
		})
		if err != nil {
			tool.logger.Errorf("Error getting cost and usage: %v", err)
			return nil, err
		}
		result := make(map[string]float64)
		var total float64
		for _, group := range resp.ResultsByTime[0].Groups {
			amount := cast.ToFloat64(*(group.Metrics["BlendedCost"].Amount))
			total = amount + total
			result[group.Keys[0]] = amount
		}
		return map[string]any{
			"total": total,
			"list":  result,
		}, nil
	}
}

func (tool *AwsBillingTool) addDay(date string, day int) string {
	t, err := time.Parse("2006-01-02", date)
	if err != nil {
		tool.logger.Error("parse date error", err)
		return date
	}
	return t.AddDate(0, 0, day).Format("2006-01-02")
}
