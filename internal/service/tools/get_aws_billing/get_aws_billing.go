package get_aws_billing

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/costexplorer"
	"github.com/aws/aws-sdk-go-v2/service/costexplorer/types"
	config2 "github.com/run-bigpig/hongdou/internal/config"
	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

// AwsBillingTool 实现了 Tool 接口，提供AWS账单查询功能
type AwsBillingTool struct {
	ctx         context.Context
	logger      logx.Logger
	accessKeyId string
	secretKey   string
}

func New(ctx context.Context, awsConfig *config2.AwsConfig) *AwsBillingTool {
	return &AwsBillingTool{
		ctx:         ctx,
		logger:      logx.WithContext(ctx),
		accessKeyId: awsConfig.AccessKeyId,
		secretKey:   awsConfig.SecretAccessKey,
	}
}

// Name 返回工具名称
func (t *AwsBillingTool) Name() string {
	return "get_aws_billing"
}

// Description 返回工具描述
func (t *AwsBillingTool) Description() string {
	return "Get the billing details of Amazon AWS cloud service within the specified date range. This tool is suitable for users to inquire about AWS fees, costs or usage in a specific period of time. It can provide detailed billing information to help users analyze and manage the overhead of cloud resources."
}

// Parameters 返回工具接受的参数规范
func (t *AwsBillingTool) Parameters() map[string]interfaces.ParameterSpec {
	return map[string]interfaces.ParameterSpec{
		"start": {
			Type:        "string",
			Description: "账单查询的开始日期，格式为YYYY-MM-DD。例如：'2023-01-01'",
			Required:    true,
		},
		"end": {
			Type:        "string",
			Description: "账单查询的结束日期，格式为YYYY-MM-DD。例如：'2023-01-31'。必须晚于开始日期",
			Required:    true,
		},
	}
}

// Run 执行工具并返回AWS账单信息
func (t *AwsBillingTool) Run(ctx context.Context, input string) (string, error) {
	var params struct {
		Start string `json:"start"`
		End   string `json:"end"`
	}

	if err := json.Unmarshal([]byte(input), &params); err != nil {
		t.logger.Errorf("解析参数失败: %v", err)
		return "", fmt.Errorf("解析参数失败: %w", err)
	}

	billingData, err := t.getBillingData(ctx, params.Start, params.End)
	if err != nil {
		t.logger.Errorf("获取AWS账单信息失败: %v", err)
		return "", err
	}

	return t.formatBillingResponse(billingData), nil
}

// Execute 与 Run 相同，但接受参数字符串
func (t *AwsBillingTool) Execute(ctx context.Context, args string) (string, error) {
	return t.Run(ctx, args)
}

// billingResponse 表示账单查询响应
type billingResponse struct {
	Total    float64            `json:"total"`
	Services map[string]float64 `json:"services"`
	Period   string             `json:"period"`
}

// getBillingData 获取AWS账单数据
func (t *AwsBillingTool) getBillingData(ctx context.Context, start, end string) (*billingResponse, error) {
	// 结束日期加一天以包含结束日期当天
	endDate := t.addDay(end, 1)

	// 获取AWS配置 - 在实际使用中应该从配置文件或环境变量获取
	// 这里需要根据实际的配置系统进行调整
	conf, err := config.LoadDefaultConfig(ctx,
		config.WithRegion("us-east-1"),
		// 注意：在实际实现中，应该从安全的配置源获取凭证
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(t.accessKeyId, t.secretKey, "")),
	)
	if err != nil {
		return nil, fmt.Errorf("加载AWS配置失败: %w", err)
	}

	client := costexplorer.NewFromConfig(conf)
	resp, err := client.GetCostAndUsage(ctx, &costexplorer.GetCostAndUsageInput{
		Granularity: "MONTHLY",
		GroupBy: []types.GroupDefinition{
			{
				Type: types.GroupDefinitionTypeDimension,
				Key:  aws.String("SERVICE"),
			},
		},
		TimePeriod: &types.DateInterval{
			Start: aws.String(start),
			End:   aws.String(endDate),
		},
		Metrics: []string{"BlendedCost"},
	})
	if err != nil {
		return nil, fmt.Errorf("获取成本和使用情况失败: %w", err)
	}

	if len(resp.ResultsByTime) == 0 {
		return &billingResponse{
			Total:    0,
			Services: make(map[string]float64),
			Period:   fmt.Sprintf("%s 到 %s", start, end),
		}, nil
	}

	services := make(map[string]float64)
	var total float64

	for _, group := range resp.ResultsByTime[0].Groups {
		if blendedCost, exists := group.Metrics["BlendedCost"]; exists && blendedCost.Amount != nil {
			amount := cast.ToFloat64(*blendedCost.Amount)
			total += amount
			if len(group.Keys) > 0 {
				services[group.Keys[0]] = amount
			}
		}
	}

	return &billingResponse{
		Total:    total,
		Services: services,
		Period:   fmt.Sprintf("%s 到 %s", start, end),
	}, nil
}

// formatBillingResponse 格式化账单响应为字符串
func (t *AwsBillingTool) formatBillingResponse(data *billingResponse) string {
	result := fmt.Sprintf("AWS账单查询结果 (%s):\n", data.Period)
	result += fmt.Sprintf("总费用: $%.2f\n\n", data.Total)

	if len(data.Services) > 0 {
		result += "各服务费用明细:\n"
		for service, cost := range data.Services {
			result += fmt.Sprintf("- %s: $%.2f\n", service, cost)
		}
	} else {
		result += "该时间段内无费用记录\n"
	}

	return result
}

// addDay 为日期添加指定天数
func (t *AwsBillingTool) addDay(date string, day int) string {
	parsedTime, err := time.Parse("2006-01-02", date)
	if err != nil {
		t.logger.Errorf("解析日期失败: %v", err)
		return date
	}
	return parsedTime.AddDate(0, 0, day).Format("2006-01-02")
}
