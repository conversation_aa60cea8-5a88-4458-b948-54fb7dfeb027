package get_mongo_slow_query

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/run-bigpig/hongdou/internal/config"
	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/zeromicro/go-zero/core/logx"
	"go.mongodb.org/atlas-sdk/v20250312001/admin"
	"sort"
	"strings"
	"sync"
	"time"
)

// MongoSlowQueryTool 实现了 Tool 接口，提供MongoDB慢查询分析功能
type MongoSlowQueryTool struct {
	ctx       context.Context
	logger    logx.Logger
	apiKey    string
	apiSecret string
	groupId   string
}

// SlowQueryAttr 慢查询属性
type SlowQueryAttr struct {
	AppName        string `json:"appName"`
	DurationMillis int64  `json:"durationMillis"`
}

// SlowQuery 慢查询结构
type SlowQuery struct {
	Attr SlowQueryAttr `json:"attr"`
}

func New(ctx context.Context, atlas *config.AtlasConfig) *MongoSlowQueryTool {
	return &MongoSlowQueryTool{
		ctx:       ctx,
		logger:    logx.WithContext(ctx),
		apiKey:    atlas.ApiKey,
		apiSecret: atlas.ApiSecret,
		groupId:   atlas.GroupId,
	}
}

// Name 返回工具名称
func (t *MongoSlowQueryTool) Name() string {
	return "get_mongo_slow_query"
}

// Description 返回工具描述
func (t *MongoSlowQueryTool) Description() string {
	return "获取MongoDB集群的慢查询日志。提供指定时间段内的慢查询统计信息。mongo slow query"
}

// Parameters 返回工具接受的参数规范
func (t *MongoSlowQueryTool) Parameters() map[string]interfaces.ParameterSpec {
	return map[string]interfaces.ParameterSpec{
		"cluster": {
			Type:        "string",
			Description: "MongoDB集群名称",
			Required:    true,
			Enum:        []interface{}{"kunggame-data-prod", "kunggame-data-test"},
		},
		"number": {
			Type:        "number",
			Description: "返回的慢查询日志最大数量，不应超过10",
			Required:    true,
			Default:     3,
		},
		"duration": {
			Type:        "number",
			Description: "慢查询日志的持续时间（毫秒），例如：24小时=86400000",
			Required:    false,
			Default:     86400000, // 24小时
		},
	}
}

// Run 执行工具并返回MongoDB慢查询分析结果
func (t *MongoSlowQueryTool) Run(ctx context.Context, input string) (string, error) {
	var params struct {
		Cluster  string  `json:"cluster"`
		Number   float64 `json:"number"`
		Duration float64 `json:"duration"`
	}

	if err := json.Unmarshal([]byte(input), &params); err != nil {
		t.logger.Errorf("解析参数失败: %v", err)
		return "", fmt.Errorf("解析参数失败: %w", err)
	}

	// 设置默认值
	if params.Duration == 0 {
		params.Duration = 86400000 // 持续24小时
	}
	if params.Number == 0 {
		params.Number = 3
	}

	slowQueryData, err := t.getSlowQueryData(ctx, params.Cluster, int(params.Number), int64(params.Duration))
	if err != nil {
		t.logger.Errorf("获取MongoDB慢查询信息失败: %v", err)
		return "", err
	}

	return t.formatSlowQueryResponse(slowQueryData), nil
}

// Execute 与 Run 相同，但接受参数字符串
func (t *MongoSlowQueryTool) Execute(ctx context.Context, args string) (string, error) {
	return t.Run(ctx, args)
}

// slowQueryResponse 表示慢查询分析响应
type slowQueryResponse struct {
	NowTime               string   `json:"nowTime"`
	MaxSlowDuration       int64    `json:"maxSlowDuration"`
	MoreOneSecondSlowLogs int      `json:"moreOneSecondSlowLogs"`
	TopLogs               []string `json:"topLogs"`
	Message               string   `json:"message"`
	Period                string   `json:"period"`
}

// getSlowQueryData 获取MongoDB慢查询数据
func (t *MongoSlowQueryTool) getSlowQueryData(ctx context.Context, cluster string, number int, duration int64) (*slowQueryResponse, error) {
	// 在实际实现中，应该从安全的配置源获取凭证
	// 这里需要根据实际的配置系统进行调整
	sdk, err := admin.NewClient(admin.UseDigestAuth(t.apiKey, t.apiSecret))
	if err != nil {
		return nil, fmt.Errorf("创建MongoDB Atlas客户端失败: %w", err)
	}

	// 获取所有的进程
	processes, response, err := sdk.MonitoringAndLogsApi.ListAtlasProcesses(ctx, t.groupId).Execute()
	if err != nil {
		return nil, fmt.Errorf("获取Atlas进程列表失败: %w", err)
	}
	if response.StatusCode != 200 {
		return nil, fmt.Errorf("获取Atlas进程列表失败，状态码: %d", response.StatusCode)
	}

	slowQueries := make([]string, 0)
	shardName := fmt.Sprintf("%s-shard", cluster)
	wg := sync.WaitGroup{}
	resultChan := make(chan string, 100000)
	sem := make(chan struct{}, 5) // 控制并发为5

	// 获取慢查询，控制并发为5
	for _, process := range processes.GetResults() {
		if !strings.Contains(process.GetUserAlias(), shardName) || strings.Contains(process.GetTypeName(), "MONGOS") {
			continue
		}
		sem <- struct{}{}
		wg.Add(1)
		go func(processId string) {
			defer func() {
				<-sem
				wg.Done()
			}()

			slowQuery, response, err := sdk.PerformanceAdvisorApi.ListSlowQueriesWithParams(ctx, &admin.ListSlowQueriesApiParams{
				GroupId:   t.groupId,
				ProcessId: processId,
				Duration:  &duration,
			}).Execute()
			if err != nil {
				t.logger.Errorf("获取慢查询失败: %v", err)
				return
			}
			if response.StatusCode != 200 {
				t.logger.Errorf("获取慢查询失败，状态码: %d", response.StatusCode)
				return
			}
			if slowQuery == nil {
				return
			}
			for _, v := range slowQuery.GetSlowQueries() {
				resultChan <- v.GetLine()
			}
		}(process.GetId())
	}

	wg.Wait()
	close(resultChan)

	for s := range resultChan {
		slowQueries = append(slowQueries, s)
	}

	return t.processSlowQueries(slowQueries, cluster, number), nil
}

// processSlowQueries 处理慢查询数据
func (t *MongoSlowQueryTool) processSlowQueries(slowQueries []string, cluster string, number int) *slowQueryResponse {
	nowTime := time.Now().UTC().Format("2006-01-02 15:04:05 UTC")

	// 排序
	durationSlice := make([]int64, 0)
	recordMap := make(map[int64]string)

	for _, s := range slowQueries {
		var slowQuery SlowQuery
		err := json.Unmarshal([]byte(s), &slowQuery)
		if err != nil {
			t.logger.Errorf("解析慢查询记录失败: %v", err)
			continue
		}

		// 大于1秒的慢查询记录
		if slowQuery.Attr.DurationMillis > 1000 && slowQuery.Attr.AppName == cluster {
			durationSlice = append(durationSlice, slowQuery.Attr.DurationMillis)
			recordMap[slowQuery.Attr.DurationMillis] = s
		}
	}

	if len(durationSlice) == 0 {
		return &slowQueryResponse{
			NowTime:               nowTime,
			MaxSlowDuration:       0,
			MoreOneSecondSlowLogs: 0,
			TopLogs:               []string{},
			Message:               "未找到超过1秒的慢查询",
			Period:                fmt.Sprintf("集群: %s", cluster),
		}
	}

	// 取耗时前n的记录
	sort.Slice(durationSlice, func(i, j int) bool {
		return durationSlice[i] > durationSlice[j]
	})

	index := number
	if len(durationSlice) < number {
		index = len(durationSlice)
	}

	topLogs := make([]string, 0, index)
	for i := 0; i < index; i++ {
		topLogs = append(topLogs, recordMap[durationSlice[i]])
	}

	return &slowQueryResponse{
		NowTime:               nowTime,
		MaxSlowDuration:       durationSlice[0],
		MoreOneSecondSlowLogs: len(durationSlice),
		TopLogs:               topLogs,
		Message:               "查询成功",
		Period:                fmt.Sprintf("集群: %s", cluster),
	}
}

// formatSlowQueryResponse 格式化慢查询响应为字符串
func (t *MongoSlowQueryTool) formatSlowQueryResponse(data *slowQueryResponse) string {
	result := fmt.Sprintf("MongoDB慢查询分析结果 (%s)\n", data.Period)
	result += fmt.Sprintf("查询时间: %s\n", data.NowTime)
	result += fmt.Sprintf("状态: %s\n\n", data.Message)

	if data.MoreOneSecondSlowLogs == 0 {
		result += "该时间段内无超过1秒的慢查询记录\n"
		return result
	}

	result += fmt.Sprintf("慢查询统计:\n")
	result += fmt.Sprintf("- 超过1秒的慢查询总数: %d\n", data.MoreOneSecondSlowLogs)
	result += fmt.Sprintf("- 最慢查询耗时: %d毫秒 (%.2f秒)\n\n", data.MaxSlowDuration, float64(data.MaxSlowDuration)/1000.0)

	if len(data.TopLogs) > 0 {
		result += fmt.Sprintf("Top %d 慢查询详情:\n", len(data.TopLogs))
		for i, log := range data.TopLogs {
			result += fmt.Sprintf("%d. %s\n", i+1, log)
		}
	}

	return result
}
