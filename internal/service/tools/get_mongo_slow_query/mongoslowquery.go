package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"game/api/mcp/internal/svc"
	"game/common/utils"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/mcp"
	"go.mongodb.org/atlas-sdk/v20250312001/admin"
	"log"
	"sort"
	"strings"
	"sync"
	"time"
)

type MongoSlowQueryTool struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logger logx.Logger
	tool   mcp.Tool
}

type SlowQueryAttr struct {
	AppName        string `json:"appName"`
	DurationMillis int64  `json:"durationMillis"`
}

type SlowQuery struct {
	Attr SlowQueryAttr `json:"attr"`
}

func NewMongoSlowQueryTool(ctx context.Context, svcCtx *svc.ServiceContext, mcpServer mcp.McpServer) {
	t := &MongoSlowQueryTool{
		ctx:    ctx,
		svcCtx: svcCtx,
		logger: logx.WithContext(ctx),
		tool: mcp.Tool{
			Name:        "mongoSlowQuery",
			Description: "mongo slow query",
		},
	}
	t.setInputSchema()
	t.setHandler()
	if err := mcpServer.RegisterTool(t.tool); err != nil {
		log.Fatalf("Error registering tool: %v", err)
	}
}

func (tool *MongoSlowQueryTool) setInputSchema() {
	tool.tool.InputSchema = mcp.InputSchema{
		Properties: map[string]any{
			"cluster": map[string]any{
				"type":        "string",
				"description": "mongo cluster name",
				"enum":        []string{"kunggame-data-prod", "kunggame-data-test"},
			},
			"number": map[string]any{
				"type":        "number",
				"description": "The maximum number of returned slow query logs shall not exceed 10",
			},
			"duration": map[string]any{
				"type":        "number",
				"description": "The duration of the slow query log in milliseconds example: 24h=86400000",
			},
		},
		Required: []string{"cluster", "number"},
	}
}

func (tool *MongoSlowQueryTool) setHandler() {
	tool.tool.Handler = func(ctx context.Context, params map[string]any) (any, error) {
		var req struct {
			Cluster  string  `json:"cluster"`
			Number   float64 `json:"number"`
			Duration float64 `json:"duration"`
		}
		if err := mcp.ParseArguments(params, &req); err != nil {
			return nil, err
		}
		if req.Duration == 0 {
			req.Duration = 86400000 //持续24小时
		}
		if req.Number == 0 {
			req.Number = 3
		}
		sdk, err := admin.NewClient(admin.UseDigestAuth(tool.svcCtx.Config.AtlasConf.ApiKey, tool.svcCtx.Config.AtlasConf.ApiSecret))
		if err != nil {
			tool.logger.Errorf("Error creating client: %v", err)
			return nil, err
		}
		//获取所有的进程
		processes, response, err := sdk.MonitoringAndLogsApi.ListAtlasProcesses(ctx, tool.svcCtx.Config.AtlasConf.GroupId).Execute()
		if err != nil {
			tool.logger.Errorf("Error listing processes: %v", err)
			return nil, err
		}
		if response.StatusCode != 200 {
			tool.logger.Errorf("Error listing processes: %v", err)
			return nil, err
		}
		slowQuerys := make([]string, 0)
		shardName := fmt.Sprintf("%s-shard", req.Cluster)
		wg := sync.WaitGroup{}
		resultChan := make(chan string, 100000)
		sem := make(chan struct{}, 5)
		nowTimeStr, _ := utils.ConvertTimestampToTimeZone(time.Now().Unix(), "UTC")
		//获取慢查询控制并发为5
		for _, process := range processes.GetResults() {
			if !strings.Contains(process.GetUserAlias(), shardName) || strings.Contains(process.GetTypeName(), "MONGOS") {
				continue
			}
			sem <- struct{}{}
			wg.Add(1)
			go func() {
				defer func() {
					<-sem
					wg.Done()
				}()
				var slowQuery, response, err = sdk.PerformanceAdvisorApi.ListSlowQueriesWithParams(ctx, &admin.ListSlowQueriesApiParams{
					GroupId:   tool.svcCtx.Config.AtlasConf.GroupId,
					ProcessId: process.GetId(),
					Duration:  utils.Int64Ptr(int64(req.Duration)),
				}).Execute()
				if err != nil {
					tool.logger.Errorf("Error listing slow queries: %v", err)
					return
				}
				if response.StatusCode != 200 {
					tool.logger.Errorf("Error listing slow queries: %v", err)
					return
				}
				if slowQuery == nil {
					return
				}
				for _, v := range slowQuery.GetSlowQueries() {
					resultChan <- v.GetLine()
				}
			}()
		}
		wg.Wait()
		close(resultChan)
		for s := range resultChan {
			slowQuerys = append(slowQuerys, s)
		}
		//排序
		durationSlice := make([]int64, 0)
		recordMap := make(map[int64]string)
		for _, s := range slowQuerys {
			var slowQuery SlowQuery
			err = json.Unmarshal([]byte(s), &slowQuery)
			if err != nil {
				log.Fatalf("Error unmarshal slow query: %v", err)
			}
			//大于1秒的慢查询记录
			if slowQuery.Attr.DurationMillis > 1000 && slowQuery.Attr.AppName == req.Cluster {
				durationSlice = append(durationSlice, slowQuery.Attr.DurationMillis)
				recordMap[slowQuery.Attr.DurationMillis] = s
			}
		}
		if len(durationSlice) == 0 {
			return map[string]any{
				"nowTime":               nowTimeStr,
				"maxSlowDuration":       0,
				"MoreOneSecondSlowLogs": 0,
				"topLogs":               []string{},
				"msg":                   "No slow queries exceeding 1 second found",
			}, nil
		}
		index := int(req.Number)
		topLogs := make([]string, 0)
		//取耗时前n的记录
		sort.Slice(durationSlice, func(i, j int) bool {
			return durationSlice[i] > durationSlice[j]
		})
		if len(durationSlice) < int(req.Number) {
			index = len(durationSlice)
		}
		for i := 0; i < index; i++ {
			topLogs = append(topLogs, recordMap[durationSlice[i]])
		}
		return map[string]any{
			"nowTime":               nowTimeStr,
			"maxSlowDuration":       durationSlice[0],
			"MoreOneSecondSlowLogs": len(durationSlice),
			"topLogs":               topLogs,
			"msg":                   "success",
		}, nil
	}
}
