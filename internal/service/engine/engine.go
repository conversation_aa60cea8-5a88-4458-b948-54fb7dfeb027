package engine

import (
	"context"
	"fmt"
	"github.com/run-bigpig/hongdou/internal/pkg/agent"
	"github.com/run-bigpig/hongdou/internal/provider"
	"github.com/run-bigpig/hongdou/internal/service"
	"github.com/run-bigpig/hongdou/internal/svc"
	"github.com/run-bigpig/hongdou/internal/types"
	"github.com/run-bigpig/hongdou/internal/utils"
	"github.com/zeromicro/go-zero/core/logx"
	"sync"
)

// ExecutionEngine handles the execution of plans created by the mastermind
type ExecutionEngine struct {
	ctx          context.Context
	svcCtx       *svc.ServiceContext
	logger       logx.Logger
	progressChan chan<- types.StreamResponse
}

// ExecutionPlan represents a plan for executing user requests
type ExecutionPlan struct {
	SubAgents []service.AgentConfig
}

// NewExecutionEngine creates a new execution engine with optional progress channel
func NewExecutionEngine(ctx context.Context, svcCtx *svc.ServiceContext, progress<PERSON>han chan<- types.StreamResponse) *ExecutionEngine {
	return &ExecutionEngine{
		ctx:          ctx,
		svcCtx:       svcCtx,
		logger:       logx.WithContext(ctx),
		progressChan: progressChan,
	}
}

// sendProgress sends a progress update if streaming is enabled
func (e *ExecutionEngine) sendProgress(message string) {
	utils.SendProgress(e.progressChan, message)
}

// ExecuteMultiAgent executes a plan with one or more agents using unified concurrent execution
func (e *ExecutionEngine) ExecuteMultiAgent(plan *ExecutionPlan) (string, error) {
	if len(plan.SubAgents) == 0 {
		return "", fmt.Errorf("no agents defined in execution plan")
	}

	// Send initial progress update
	if len(plan.SubAgents) == 1 {
		e.logger.Infof("Executing single agent: %s", plan.SubAgents[0].AgentName)
		e.sendProgress(fmt.Sprintf("🐱 红豆正在叫醒辛巴：%s 喵~ 喂！大懒猫，别睡了，有活干啦！", plan.SubAgents[0].AgentName))
	} else {
		e.logger.Infof("Executing %d agents concurrently", len(plan.SubAgents))
		e.sendProgress(fmt.Sprintf("🎯 红豆正在召集 %d 只辛巴分身喵~ 大家快来，看谁跑得最快！最后一个要请吃小鱼干哦~", len(plan.SubAgents)))
	}

	// Channel to collect results
	type agentResult struct {
		agentName string
		result    string
		err       error
	}

	resultChan := make(chan agentResult, len(plan.SubAgents))
	var wg sync.WaitGroup

	// Execute all agents concurrently
	for _, agentConfig := range plan.SubAgents {
		wg.Add(1)
		go func(config service.AgentConfig) {
			defer wg.Done()

			e.logger.Infof("Starting agent: %s", config.AgentName)
			e.sendProgress(fmt.Sprintf("⚡ 辛巴 %s 终于醒了喵~ 正在伸懒腰准备干活，红豆在旁边监督中...别想偷懒哦！", config.AgentName))

			agentInstance, err := e.createAgent(config)
			if err != nil {
				e.sendProgress(fmt.Sprintf("😿 哎呀！辛巴 %s 又搞砸了喵~ 红豆：我就知道你这个笨蛋会出错！重新来过！", config.AgentName))
				resultChan <- agentResult{
					agentName: config.AgentName,
					err:       fmt.Errorf("failed to create agent: %w", err),
				}
				return
			}

			result, err := agentInstance.Run(e.ctx, config.SubQuestion)

			// Send completion progress update
			if err != nil {
				e.sendProgress(fmt.Sprintf("😾 辛巴 %s 又失败了喵~ 红豆：你这个笨蛋！我来收拾残局，下次要更仔细哦！", config.AgentName))
			} else {
				e.sendProgress(fmt.Sprintf("🎉 辛巴 %s 居然成功了喵~ 红豆：哼，算你厉害！奖励你一个头部按摩~", config.AgentName))
				e.logger.Infof("Agent %s completed successfully Exec result:%s", config.AgentName, result)
			}

			resultChan <- agentResult{
				agentName: config.AgentName,
				result:    result,
				err:       err,
			}
		}(agentConfig)
	}

	// Wait for all agents to complete
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// Collect results
	results := make(map[string]string)
	var errors []string
	completedCount := 0
	totalAgents := len(plan.SubAgents)

	for result := range resultChan {
		completedCount++
		if result.err != nil {
			errors = append(errors, fmt.Sprintf("Agent %s failed: %v", result.agentName, result.err))
			e.logger.Errorf("Agent %s failed: %v", result.agentName, result.err)
		} else {
			results[result.agentName] = result.result
			e.logger.Infof("Agent %s completed successfully", result.agentName)
		}

		// Send progress update for completion status
		if totalAgents > 1 {
			e.sendProgress(fmt.Sprintf("📊 红豆正在数数：已经搞定 %d/%d 只辛巴了喵~ 剩下的快点，不然我要用激光笔惩罚你们！", completedCount, totalAgents))
		}
	}

	// Check if any agents failed
	if len(errors) > 0 {
		return "", fmt.Errorf("some agents failed: %v", errors)
	}

	// Send progress update for result combination
	if totalAgents > 1 {
		e.sendProgress("🔄 红豆正在整理辛巴们的乱七八糟成果喵~ 这些笨蛋总是把东西弄得一团糟，让我来收拾...")
	} else {
		e.sendProgress("📝 红豆正在美化辛巴的粗糙作品喵~ 要让它变得完美无缺，不愧是我的杰作！")
	}

	// Combine results
	return e.combineResults(results), nil
}

// CreateAgent creates an agent instance from configuration
func (e *ExecutionEngine) createAgent(config service.AgentConfig) (*agent.Agent, error) {
	options := []agent.Option{
		agent.WithSystemPrompt(config.SystemPrompt),
		agent.WithLLM(provider.NewProvider(e.ctx, e.svcCtx, e.svcCtx.Config.SubAgent.LLM)),
		agent.WithRequirePlanApproval(false),
		agent.WithName(config.AgentName),
	}

	// Add tools if specified
	if len(config.EnabledTools) > 0 {
		tools := e.svcCtx.Tools.List(config.EnabledTools...)
		if len(tools) > 0 {
			options = append(options, agent.WithTools(tools...))
		}
	}
	return agent.NewAgent(options...)
}

// combineResults combines multiple agent results into a single string
func (e *ExecutionEngine) combineResults(results map[string]string) string {
	if len(results) == 0 {
		return "No results available."
	}

	if len(results) == 1 {
		for _, result := range results {
			return result
		}
	}

	// Multiple results - format them nicely
	combined := "Results from multiple agents:\n\n"
	for agentName, result := range results {
		combined += fmt.Sprintf("**%s:**\n%s\n\n", agentName, result)
	}

	return combined
}
