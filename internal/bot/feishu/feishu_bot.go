package feishu

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"

	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	larkwiki "github.com/larksuite/oapi-sdk-go/v3/service/wiki/v2"
	"github.com/run-bigpig/hongdou/internal/bot"
	"github.com/run-bigpig/hongdou/internal/config"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
)

// FeishuBot 飞书机器人实现
type FeishuBot struct {
	ctx           context.Context
	config        *config.FeishuBotConfig
	client        *lark.Client
	logger        logx.Logger
	isInitialized bool
	isHealthy     bool
}

// NewFeishuBot 创建新的飞书机器人实例
func NewFeishuBot(ctx context.Context, config *config.FeishuBotConfig) *FeishuBot {
	return &FeishuBot{
		ctx:    ctx,
		config: config,
		logger: logx.WithContext(ctx),
	}
}

// Initialize 初始化飞书机器人
func (f *FeishuBot) Initialize(ctx context.Context) error {
	if err := f.config.Validate(); err != nil {
		return fmt.Errorf("飞书机器人配置验证失败: %w", err)
	}

	f.client = lark.NewClient(f.config.AppId, f.config.AppSecret)
	f.isInitialized = true
	f.isHealthy = true

	f.logger.Infof("飞书机器人初始化成功，机器人名称: %s", f.config.BotName)
	return nil
}

// HandleEvent 处理来自飞书的事件
func (f *FeishuBot) HandleEvent(ctx context.Context, event *bot.Event) error {
	if !f.isInitialized {
		return errors.New("飞书机器人未初始化")
	}

	switch event.Type {
	case "im.message.receive_v1":
		return f.handleImMessage(ctx, event)
	case "im.chat.member.user.added_v1":
		return f.handleP2PJoinChat(ctx, event)
	default:
		f.logger.Warnf("未知的事件类型: %s", event.Type)
		return nil
	}
}

// handleImMessage 处理即时消息事件
func (f *FeishuBot) handleImMessage(ctx context.Context, event *bot.Event) error {
	dataBytes, err := json.Marshal(event.Data)
	if err != nil {
		return fmt.Errorf("序列化事件数据失败: %w", err)
	}

	var message bot.ImMessageReceiveEvent
	if err := json.Unmarshal(dataBytes, &message); err != nil {
		return fmt.Errorf("解析即时消息事件失败: %w", err)
	}

	// 判断当前消息是否@了机器人
	if message.Message.ChatType == "group" {
		var botMentioned bool
		for _, mention := range message.Message.Mentions {
			if mention.Name == f.config.BotName {
				botMentioned = true
				break
			}
		}
		if !botMentioned {
			return nil
		}
	}

	switch message.Message.MessageType {
	case "text":
		return f.handleTextMessage(ctx, &message)
	default:
		f.logger.Errorf("不支持的消息类型: %s", message.Message.MessageType)
		return errors.New("不支持的消息类型")
	}
}

// handleP2PJoinChat 处理P2P聊天加入事件
func (f *FeishuBot) handleP2PJoinChat(ctx context.Context, event *bot.Event) error {
	dataBytes, err := json.Marshal(event.Data)
	if err != nil {
		return fmt.Errorf("序列化事件数据失败: %w", err)
	}

	var joinEvent bot.P2PJoinChat
	if err := json.Unmarshal(dataBytes, &joinEvent); err != nil {
		return fmt.Errorf("解析P2P加入事件失败: %w", err)
	}

	// 可以在这里发送欢迎消息
	// return f.SendTextMessage(ctx, "p2p", joinEvent.OperatorId.OpenID, "您好，我是智能助手，有什么可以帮您的吗？")
	return nil
}

// handleTextMessage 处理文本消息
func (f *FeishuBot) handleTextMessage(ctx context.Context, message *bot.ImMessageReceiveEvent) error {
	if message.Message.Content == "" {
		return errors.New("消息内容为空")
	}

	var content struct {
		Text string `json:"text"`
	}
	if err := json.Unmarshal([]byte(message.Message.Content), &content); err != nil {
		return fmt.Errorf("解析消息内容失败: %w", err)
	}

	// 表情回复收到处理请求
	emojis := []string{"SaluteFace", "OK", "Get", "OneSecond"}
	err := f.ReplyEmojiMessage(ctx, message.Message.MessageID, emojis[rand.Intn(len(emojis))])
	if err != nil {
		f.logger.Errorf("回复表情失败: %v", err)
	}

	// 处理指令
	text := f.dealText(content.Text)
	if strings.Contains(text, "/cmd ") {
		return f.ProcessDirective(ctx, message, text)
	}

	// 接入工作流处理
	return f.ProcessWorkflow(ctx, message, text)
}

// SendTextMessage 发送文本消息
func (f *FeishuBot) SendTextMessage(ctx context.Context, chatType, chatId, text string) error {
	if !f.isInitialized {
		return errors.New("飞书机器人未初始化")
	}

	idType := "open_id"
	if chatType == "group" {
		idType = "chat_id"
	}

	var content struct {
		Text string `json:"text"`
	}
	content.Text = text
	textBytes, err := json.Marshal(content)
	if err != nil {
		return fmt.Errorf("序列化文本内容失败: %w", err)
	}

	req := larkim.NewCreateMessageReqBuilder().ReceiveIdType(idType).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			ReceiveId(chatId).
			MsgType("text").
			Content(string(textBytes)).
			Build()).
		Build()

	resp, err := f.client.Im.V1.Message.Create(ctx, req)
	if err != nil {
		return fmt.Errorf("发送文本消息失败: %w", err)
	}

	if !resp.Success() {
		return fmt.Errorf("服务器错误 - code:%d, msg:%s, req_id:%s",
			resp.Code, resp.Msg, resp.RequestId())
	}

	return nil
}

// SendInteractiveMessage 发送交互式消息（卡片）
func (f *FeishuBot) SendInteractiveMessage(ctx context.Context, messageId string, cardData *bot.CardData) error {
	if !f.isInitialized {
		return errors.New("飞书机器人未初始化")
	}

	replyContent := bot.Card{
		Type: "template",
		Data: cardData,
	}

	replyContentBytes, err := json.Marshal(replyContent)
	if err != nil {
		return fmt.Errorf("序列化卡片内容失败: %w", err)
	}

	req := larkim.NewReplyMessageReqBuilder().
		MessageId(messageId).
		Body(larkim.NewReplyMessageReqBodyBuilder().
			Content(string(replyContentBytes)).
			MsgType("interactive").
			ReplyInThread(false).
			Build()).
		Build()

	resp, err := f.client.Im.V1.Message.Reply(ctx, req)
	if err != nil {
		return fmt.Errorf("发送交互式消息失败: %w", err)
	}

	if !resp.Success() {
		return fmt.Errorf("服务器错误 - code:%d, msg:%s, req_id:%s",
			resp.Code, resp.Msg, resp.RequestId())
	}

	return nil
}

// ReplyEmojiMessage 回复表情消息
func (f *FeishuBot) ReplyEmojiMessage(ctx context.Context, messageId string, emoji string) error {
	if !f.isInitialized {
		return errors.New("飞书机器人未初始化")
	}

	req := larkim.NewCreateMessageReactionReqBuilder().
		MessageId(messageId).
		Body(larkim.NewCreateMessageReactionReqBodyBuilder().
			ReactionType(larkim.NewEmojiBuilder().
				EmojiType(emoji).
				Build()).
			Build()).
		Build()

	resp, err := f.client.Im.V1.MessageReaction.Create(ctx, req)
	if err != nil {
		return fmt.Errorf("回复表情失败: %w", err)
	}

	if !resp.Success() {
		return fmt.Errorf("服务器错误 - code:%d, msg:%s, req_id:%s",
			resp.Code, resp.Msg, resp.RequestId())
	}

	return nil
}

// GetPlatform 获取机器人平台类型
func (f *FeishuBot) GetPlatform() bot.BotPlatform {
	return bot.PlatformFeishu
}

// IsHealthy 检查机器人健康状态
func (f *FeishuBot) IsHealthy() bool {
	return f.isHealthy && f.isInitialized
}

// Cleanup 清理资源
func (f *FeishuBot) Cleanup() error {
	f.isInitialized = false
	f.isHealthy = false
	f.logger.Info("飞书机器人资源清理完成")
	return nil
}

// dealText 移除消息中@用户的部分
func (f *FeishuBot) dealText(text string) string {
	re := regexp.MustCompile(`@_user_[a-zA-Z0-9-]+`)
	text = re.ReplaceAllString(text, "")
	return strings.TrimSpace(text)
}

// GetHistoryMessages 获取历史消息
func (f *FeishuBot) GetHistoryMessages(ctx context.Context, chatId string, startTime, endTime time.Time, limit int) (*bot.HistoryMessage, error) {
	if !f.isInitialized {
		return nil, errors.New("飞书机器人未初始化")
	}

	req := larkim.NewListMessageReqBuilder().
		ContainerIdType("chat").
		ContainerId(chatId).
		StartTime(cast.ToString(startTime.Unix())).
		EndTime(cast.ToString(endTime.Unix())).
		SortType("ByCreateTimeDesc").
		PageSize(int32(limit)).
		Build()

	resp, err := f.client.Im.V1.Message.List(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("获取历史消息失败: %w", err)
	}

	if !resp.Success() {
		return nil, fmt.Errorf("服务器错误 - code:%d, msg:%s, req_id:%s",
			resp.Code, resp.Msg, resp.RequestId())
	}

	if resp.Data == nil {
		return nil, errors.New("没有获取到历史消息")
	}

	// 将消息按照创建时间升序排列
	sort.Slice(resp.Data.Items, func(i, j int) bool {
		return cast.ToInt64(resp.Data.Items[i].CreateTime) < cast.ToInt64(resp.Data.Items[j].CreateTime)
	})

	// 提取历史消息
	var texts []*bot.TextData
	var files []*bot.FileData

	for _, v := range resp.Data.Items {
		if v.Sender.SenderType != nil && (*v.Sender.SenderType == "user" || *v.Sender.SenderType == "app") {
			switch *v.MsgType {
			case "file":
				var file bot.FileData
				if err := json.Unmarshal([]byte(*v.Body.Content), &file); err != nil {
					continue
				}
				file.MessageId = *v.MessageId
				files = append(files, &file)
			case "text":
				var text bot.TextData
				if err := json.Unmarshal([]byte(*v.Body.Content), &text); err != nil {
					continue
				}
				wikiUrl := f.isFeiShuWiki(text.Text)
				if wikiUrl != "" {
					file, err := f.getFeiShuWikiContent(ctx, wikiUrl)
					if err == nil {
						file.MessageId = *v.MessageId
						files = append(files, file)
					}
				} else {
					text.MessageId = *v.MessageId
					texts = append(texts, &text)
				}
			}
		}
	}

	return &bot.HistoryMessage{
		Files: files,
		Texts: texts,
	}, nil
}

// GetMessage 获取指定消息内容
func (f *FeishuBot) GetMessage(ctx context.Context, messageId string) (*bot.FileData, string, error) {
	if !f.isInitialized {
		return nil, "", errors.New("飞书机器人未初始化")
	}

	req := larkim.NewGetMessageReqBuilder().
		MessageId(messageId).
		UserIdType("open_id").
		Build()

	resp, err := f.client.Im.V1.Message.Get(ctx, req)
	if err != nil {
		return nil, "", fmt.Errorf("获取消息失败: %w", err)
	}

	if !resp.Success() {
		return nil, "", fmt.Errorf("服务器错误 - code:%d, msg:%s, req_id:%s",
			resp.Code, resp.Msg, resp.RequestId())
	}

	if len(resp.Data.Items) == 0 {
		return nil, "", errors.New("消息不存在")
	}

	message := resp.Data.Items[0]
	switch *message.MsgType {
	case "text":
		var text bot.TextData
		if err := json.Unmarshal([]byte(*message.Body.Content), &text); err != nil {
			return nil, "", fmt.Errorf("解析文本消息失败: %w", err)
		}
		wikiUrl := f.isFeiShuWiki(text.Text)
		if wikiUrl != "" {
			file, err := f.getFeiShuWikiContent(ctx, wikiUrl)
			if err != nil {
				return nil, "", fmt.Errorf("获取wiki内容失败: %w", err)
			}
			file.MessageId = messageId
			return file, "", nil
		}
		return nil, text.Text, nil
	case "file":
		var file bot.FileData
		if err := json.Unmarshal([]byte(*message.Body.Content), &file); err != nil {
			return nil, "", fmt.Errorf("解析文件消息失败: %w", err)
		}
		file.MessageId = messageId
		return &file, "", nil
	case "post":
		return nil, *message.Body.Content, nil
	default:
		return nil, "", nil
	}
}

// ProcessDirective 处理指令消息
func (f *FeishuBot) ProcessDirective(ctx context.Context, message *bot.ImMessageReceiveEvent, text string) error {
	// 这里需要根据实际的指令处理器来实现
	// 由于原代码中使用了 directive.NewCommandProcessor，这里提供一个基本的实现框架
	f.logger.Infof("处理指令: %s", text)

	// 模拟指令处理结果
	result := fmt.Sprintf("指令处理结果: %s", text)

	return f.SendInteractiveMessage(ctx, message.Message.MessageID, &bot.CardData{
		TemplateID:          f.config.DirectiveCard.TemplateId,
		TemplateVersionName: f.config.DirectiveCard.TemplateVersion,
		TemplateVariable: map[string]any{
			"result": fmt.Sprintf("```JSON\n%s\n```", result),
		},
	})
}

// ProcessWorkflow 处理工作流消息
func (f *FeishuBot) ProcessWorkflow(ctx context.Context, message *bot.ImMessageReceiveEvent, text string) error {
	// 这里需要根据实际的工作流处理器来实现
	f.logger.Infof("处理工作流: %s", text)

	var historyMessageStr string
	files := make([]*bot.FileData, 0)

	// 如果有回复消息则提取回复消息内容，否则获取历史消息
	if message.Message.ParentID != "" {
		file, txt, err := f.GetMessage(ctx, message.Message.ParentID)
		if err != nil {
			return fmt.Errorf("获取回复消息失败: %w", err)
		}
		if file != nil {
			files = append(files, file)
		}
		if txt != "" {
			historyMessageStr = txt
		}
	} else {
		// 获取历史消息
		endTime := time.Now()
		startTime := endTime.Add(-10 * time.Minute) // 获取10分钟内的消息
		historyMessage, err := f.GetHistoryMessages(ctx, message.Message.ChatID, startTime, endTime, 5)
		if err == nil {
			// 添加历史文件内容
			files = append(files, historyMessage.Files...)
			// 添加历史文本内容
			if len(historyMessage.Texts) > 0 {
				for _, t := range historyMessage.Texts {
					historyMessageStr = fmt.Sprintf("%s\n%s", historyMessageStr, t.Text)
				}
			}
		}
	}

	// 解析是否包含飞书知识库地址
	wikiUrl := f.isFeiShuWiki(text)
	if wikiUrl != "" {
		fileData, err := f.getFeiShuWikiContent(ctx, wikiUrl)
		if err == nil {
			files = append(files, fileData)
		}
	}

	// 移除重复的文件
	existFile := make(map[string]bool)
	newFiles := make([]*bot.FileData, 0)
	for _, file := range files {
		if existFile[file.FileKey] {
			continue
		}
		newFiles = append(newFiles, file)
		existFile[file.FileKey] = true
	}

	// 这里应该调用实际的工作流API
	f.logger.Infof("工作流处理完成，消息ID: %s, 用户ID: %s",
		message.Message.MessageID, message.Sender.SenderID.OpenID)

	return nil
}

// getFeiShuWikiContent 获取飞书wiki内容
func (f *FeishuBot) getFeiShuWikiContent(ctx context.Context, wikiUrl string) (*bot.FileData, error) {
	u, err := url.Parse(wikiUrl)
	if err != nil {
		return nil, fmt.Errorf("解析wiki URL失败: %w", err)
	}

	pathArr := strings.Split(strings.Trim(u.Path, "/"), "/")
	if len(pathArr) != 2 {
		return nil, errors.New("wiki URL格式错误")
	}

	req := larkwiki.NewGetNodeSpaceReqBuilder().
		Token(pathArr[1]).
		Build()

	resp, err := f.client.Wiki.V2.Space.GetNode(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("获取wiki节点失败: %w", err)
	}

	if !resp.Success() {
		return nil, fmt.Errorf("服务器错误 - code:%d, msg:%s, req_id:%s",
			resp.Code, resp.Msg, resp.RequestId())
	}

	return &bot.FileData{
		WikiType: *resp.Data.Node.ObjType,
		FileKey:  *resp.Data.Node.ObjToken,
		FileName: *resp.Data.Node.Title,
	}, nil
}

// isFeiShuWiki 判断是否包含飞书云知识库内容并提取url地址
func (f *FeishuBot) isFeiShuWiki(text string) string {
	if !strings.Contains(text, "https://boke.feishu.cn/wiki/") {
		return ""
	}
	// 从字符串中提取url地址
	re := regexp.MustCompile(`https://boke.feishu.cn/wiki/[a-zA-Z0-9-]+`)
	return re.FindString(text)
}
