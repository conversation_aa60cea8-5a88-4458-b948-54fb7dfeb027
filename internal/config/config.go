package config

import "github.com/zeromicro/go-zero/rest"

type Config struct {
	rest.RestConf
	LLMConfig       LLMConfig
	MasterMind      MasterMind
	SubAgent        SubAgent
	VectorStore     VectorStoreConfig
	EmbedderConfig  EmbedderConfig
	RedisConfig     RedisConfig
	McpServerConfig McpServerConfig
	SelfToolsConfig SelfToolsConfig
}

type VectorStoreConfig struct {
	Weaviate Weaviate
}

type Weaviate struct {
	Host   string
	Schema string
	ApiKey string
}

// 主脑配置
type MasterMind struct {
	Prompt string
	LLM    string
	Model  string
}

// subAgent
type SubAgent struct {
	LLM   string
	Model string
}

type OpenAiConfig struct {
	BaseUrl string
	Sk      string
}

type VertexConfig struct {
	Project  string
	Location string
	ApiKey   string
}

type LLMConfig struct {
	OpenAi OpenAiConfig
	Vertex VertexConfig
}

type EmbedderConfig struct {
	BaseUrl             string
	Model               string
	Sk                  string
	Dimension           int
	SimilarityMetric    string
	SimilarityThreshold float32
}

type RedisConfig struct {
	Addr string
	Pass string
}

type McpServerConfig struct {
	Host string
}

type AtlasConfig struct {
	GroupId   string
	ApiKey    string
	ApiSecret string
}

type AwsConfig struct {
	AccessKeyId     string
	SecretAccessKey string
}

type SelfToolsConfig struct {
	AtlasConfig AtlasConfig
	AwsConfig   AwsConfig
}
