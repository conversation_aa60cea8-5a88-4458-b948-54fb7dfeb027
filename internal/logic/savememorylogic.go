package logic

import (
	"context"
	"github.com/run-bigpig/hongdou/internal/consts"
	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/run-bigpig/hongdou/internal/pkg/multitenancy"

	"github.com/run-bigpig/hongdou/internal/svc"
	"github.com/run-bigpig/hongdou/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SaveMemoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 保存长期记忆
func NewSaveMemoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SaveMemoryLogic {
	return &SaveMemoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SaveMemoryLogic) SaveMemory(req *types.SaveMemoryQuest) (resp *types.Empty, err error) {
	l.ctx = multitenancy.WithOrgID(l.ctx, "hongdou")
	l.ctx = multitenancy.WithUserID(l.ctx, "danxing")
	err = l.svcCtx.VectorMemory.AddMessage(l.ctx, interfaces.Message{
		OriginId: "hongdou-org",
		UserId:   "hongdou-user",
		Role:     consts.RoleKnowledge,
		Content:  req.Content,
	})
	if err != nil {
		return nil, err
	}
	return &types.Empty{}, nil
}
