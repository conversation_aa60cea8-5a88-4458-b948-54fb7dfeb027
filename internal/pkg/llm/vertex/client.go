package vertex

import (
	"cloud.google.com/go/auth/credentials"
	"context"
	"encoding/json"
	"fmt"
	"github.com/run-bigpig/hongdou/internal/pkg/logging"
	"log"
	"strings"
	"time"

	"github.com/cenkalti/backoff/v4"
	"google.golang.org/genai"

	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/run-bigpig/hongdou/internal/pkg/llm"
)

// VertexAI model constants
const (
	ModelGemini15Pro     = "gemini-1.5-pro"
	ModelGemini25Pro     = "gemini-2.5-pro"
	ModelGemini15Flash   = "gemini-1.5-flash"
	ModelGemini20Flash   = "gemini-2.0-flash-exp"
	ModelGemini25Flash   = "gemini-2.5-flash"
	ModelGeminiProVision = "gemini-pro-vision"
)

// DefaultModel is the default Vertex AI model
const DefaultModel = ModelGemini15Pro

// ReasoningMode defines the reasoning approach for the model
type ReasoningMode string

const (
	ReasoningModeNone          ReasoningMode = "none"
	ReasoningModeMinimal       ReasoningMode = "minimal"
	ReasoningModeComprehensive ReasoningMode = "comprehensive"
)

// Client represents a Vertex AI client
type Client struct {
	client          *genai.Client
	model           string
	projectID       string
	location        string
	maxRetries      int
	retryDelay      time.Duration
	reasoningMode   ReasoningMode
	logger          *logging.HDLogger
	credentialsFile string
	credentialsJson []byte
}

// ClientOption is a function that configures the Client
type ClientOption func(*Client)

// WithModel sets the model for the client
func WithModel(model string) ClientOption {
	return func(c *Client) {
		c.model = model
	}
}

// WithLocation sets the location for the client
func WithLocation(location string) ClientOption {
	return func(c *Client) {
		c.location = location
	}
}

// WithMaxRetries sets the maximum number of retries
func WithMaxRetries(maxRetries int) ClientOption {
	return func(c *Client) {
		c.maxRetries = maxRetries
	}
}

// WithRetryDelay sets the retry delay
func WithRetryDelay(delay time.Duration) ClientOption {
	return func(c *Client) {
		c.retryDelay = delay
	}
}

// WithReasoningMode sets the reasoning mode
func WithReasoningMode(mode ReasoningMode) ClientOption {
	return func(c *Client) {
		c.reasoningMode = mode
	}
}

// WithLogger sets the logger for the client
func WithLogger(logger *logging.HDLogger) ClientOption {
	return func(c *Client) {
		c.logger = logger
	}
}

// WithCredentialsFile sets the path to the service account credentials file
func WithCredentialsFile(credentialsFile string) ClientOption {
	return func(c *Client) {
		c.credentialsFile = credentialsFile
	}
}

func WithCredentialsJSON(credentialsJSON []byte) ClientOption {
	return func(c *Client) {
		c.credentialsJson = credentialsJSON
	}
}

// NewClient creates a new Vertex AI client
func NewClient(ctx context.Context, projectID string, options ...ClientOption) (*Client, error) {
	if projectID == "" {
		return nil, fmt.Errorf("projectID is required")
	}

	client := &Client{
		model:         DefaultModel,
		projectID:     projectID,
		location:      "us-central1",
		maxRetries:    3,
		retryDelay:    time.Second,
		reasoningMode: ReasoningModeNone,
		logger:        logging.New(),
	}

	// Apply options
	for _, opt := range options {
		opt(client)
	}

	// Initialize Vertex AI client
	cre, err := credentials.DetectDefault(&credentials.DetectOptions{
		Scopes:          []string{"https://www.googleapis.com/auth/cloud-platform"},
		CredentialsJSON: client.credentialsJson,
	})
	if err != nil {
		log.Fatalf("failed to get credentials: %v", err)
	}
	vertexClient, err := genai.NewClient(ctx, &genai.ClientConfig{
		Backend:     genai.BackendVertexAI,
		Project:     client.projectID,
		Location:    client.location,
		Credentials: cre,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create Vertex AI client: %w", err)
	}

	client.client = vertexClient
	return client, nil
}

// Name returns the client name
func (c *Client) Name() string {
	return fmt.Sprintf("vertex:%s", c.model)
}

// GenerateWithTools implements interfaces.LLM.GenerateWithTools
func (c *Client) GenerateWithTools(ctx context.Context, prompt string, tools []interfaces.Tool, options ...interfaces.GenerateOption) (string, error) {
	// Apply options
	params := &interfaces.GenerateOptions{
		LLMConfig: &interfaces.LLMConfig{
			Temperature: 0.7,
		},
	}

	for _, option := range options {
		option(params)
	}

	// Create generateConfig
	generateConfig := genai.GenerateContentConfig{}
	// Add system message if provided
	if params.SystemMessage != "" {
		systemMessage := params.SystemMessage

		// Apply reasoning if specified
		if params.LLMConfig != nil && params.LLMConfig.Reasoning != "" {
			switch params.LLMConfig.Reasoning {
			case "minimal":
				systemMessage = fmt.Sprintf("%s\n\nWhen responding, briefly explain your thought process.", systemMessage)
			case "comprehensive":
				systemMessage = fmt.Sprintf("%s\n\nWhen responding, please think step-by-step and explain your complete reasoning process in detail.", systemMessage)
			case "none":
				systemMessage = fmt.Sprintf("%s\n\nProvide direct, concise answers without explaining your reasoning or showing calculations.", systemMessage)
			}
		}
		generateConfig.SystemInstruction = genai.Text(systemMessage)[0]
	}
	// Configure model parameters
	if params.LLMConfig != nil {
		if params.LLMConfig.Temperature > 0 {
			temp := float32(params.LLMConfig.Temperature)
			generateConfig.Temperature = &temp
		}
		if params.LLMConfig.TopP > 0 {
			topP := float32(params.LLMConfig.TopP)
			generateConfig.TopP = &topP
		}
		if len(params.LLMConfig.StopSequences) > 0 {
			generateConfig.StopSequences = params.LLMConfig.StopSequences
		}
	}

	// Convert tools to Vertex AI format
	if len(tools) > 0 {
		vertexTools := c.convertTools(tools)
		generateConfig.Tools = vertexTools
	}
	chat, err := c.client.Chats.Create(ctx, c.model, &generateConfig, nil)
	// Generate content with retry logic
	var response *genai.GenerateContentResponse
	err = c.withRetry(ctx, func() error {
		var genErr error
		response, genErr = chat.GenerateContent(ctx, c.model, genai.Text(prompt), nil)
		return genErr
	})

	if err != nil {
		return "", fmt.Errorf("failed to generate content: %w", err)
	}

	// Extract response
	if len(response.Candidates) == 0 {
		return "", fmt.Errorf("no candidates in response")
	}

	candidate := response.Candidates[0]
	if candidate.Content == nil {
		return "", fmt.Errorf("no content in response")
	}

	var text strings.Builder
	var functionCalls []genai.FunctionCall

	for _, part := range candidate.Content.Parts {
		if part.Text != "" {
			text.WriteString(part.Text)
		}
		if part.FunctionCall != nil {
			functionCalls = append(functionCalls, *part.FunctionCall)
		}
	}

	// If there are function calls, execute them
	if len(functionCalls) > 0 {
		// Execute all function calls and collect responses
		var functionResponses []genai.Part

		for _, funcCall := range functionCalls {
			// Find the corresponding tool
			var selectedTool interfaces.Tool
			for _, tool := range tools {
				if tool.Name() == funcCall.Name {
					selectedTool = tool
					break
				}
			}

			if selectedTool == nil {
				return "", fmt.Errorf("tool not found: %s", funcCall.Name)
			}

			// Convert arguments to JSON string
			argsJSON, err := json.Marshal(funcCall.Args)
			if err != nil {
				return "", fmt.Errorf("failed to marshal function arguments: %w", err)
			}

			// Execute the tool
			toolResult, err := selectedTool.Execute(ctx, string(argsJSON))
			if err != nil {
				return "", fmt.Errorf("tool execution failed: %w", err)
			}

			// Create function response
			functionResponses = append(functionResponses, genai.Part{FunctionResponse: &genai.FunctionResponse{
				Name:     funcCall.Name,
				Response: map[string]any{"content": toolResult},
			}})
		}
		// Send function responses and get final response
		finalResponse, err := chat.SendMessage(ctx, functionResponses...)
		if err != nil {
			return "", fmt.Errorf("failed to send function responses: %w", err)
		}

		if len(finalResponse.Candidates) > 0 && finalResponse.Candidates[0].Content != nil {
			var finalText strings.Builder
			for _, part := range finalResponse.Candidates[0].Content.Parts {
				if part.Text != "" {
					finalText.WriteString(part.Text)
				}
			}
			return finalText.String(), nil
		}

		return "", fmt.Errorf("no final response received")
	}

	return text.String(), nil
}

// Generate implements interfaces.LLM.Generate
func (c *Client) Generate(ctx context.Context, prompt string, options ...interfaces.GenerateOption) (string, error) { // Apply options
	params := &interfaces.GenerateOptions{
		LLMConfig: &interfaces.LLMConfig{
			Temperature: 0.7,
		},
	}

	for _, option := range options {
		option(params)
	}

	// Create generateConfig
	generateConfig := genai.GenerateContentConfig{}
	// Add system message if provided
	if params.SystemMessage != "" {
		systemMessage := params.SystemMessage

		// Apply reasoning if specified
		if params.LLMConfig != nil && params.LLMConfig.Reasoning != "" {
			switch params.LLMConfig.Reasoning {
			case "minimal":
				systemMessage = fmt.Sprintf("%s\n\nWhen responding, briefly explain your thought process.", systemMessage)
			case "comprehensive":
				systemMessage = fmt.Sprintf("%s\n\nWhen responding, please think step-by-step and explain your complete reasoning process in detail.", systemMessage)
			case "none":
				systemMessage = fmt.Sprintf("%s\n\nProvide direct, concise answers without explaining your reasoning or showing calculations.", systemMessage)
			}
		}
		generateConfig.SystemInstruction = genai.Text(systemMessage)[0]
	}
	// Configure model parameters
	if params.LLMConfig != nil {
		if params.LLMConfig.Temperature > 0 {
			temp := float32(params.LLMConfig.Temperature)
			generateConfig.Temperature = &temp
		}
		if params.LLMConfig.TopP > 0 {
			topP := float32(params.LLMConfig.TopP)
			generateConfig.TopP = &topP
		}
		if len(params.LLMConfig.StopSequences) > 0 {
			generateConfig.StopSequences = params.LLMConfig.StopSequences
		}
	}

	//configure response format
	if params.ResponseFormat != nil {
		generateConfig.ResponseSchema = &genai.Schema{Type: params.ResponseFormat.Type}
	}

	// Convert tools to Vertex AI format
	chat, err := c.client.Chats.Create(ctx, c.model, &generateConfig, nil)
	// Generate content with retry logic
	var response *genai.GenerateContentResponse
	err = c.withRetry(ctx, func() error {
		var genErr error
		response, genErr = chat.GenerateContent(ctx, c.model, genai.Text(prompt), nil)
		return genErr
	})

	if err != nil {
		return "", fmt.Errorf("failed to generate content: %w", err)
	}

	// Extract response
	if len(response.Candidates) == 0 {
		return "", fmt.Errorf("no candidates in response")
	}

	candidate := response.Candidates[0]
	if candidate.Content == nil {
		return "", fmt.Errorf("no content in response")
	}

	var text strings.Builder

	for _, part := range candidate.Content.Parts {
		if part.Text != "" {
			text.WriteString(part.Text)
		}
	}
	return text.String(), nil
}

// convertMessages converts llm.Message to Vertex AI parts
func (c *Client) convertMessages(messages []llm.Message) ([]*genai.Content, error) {
	var parts []*genai.Content

	for _, msg := range messages {
		switch msg.Role {
		case "system":
			// System messages are handled separately in Vertex AI
			continue
		case "user", "assistant":
			parts = append(parts, genai.Text(msg.Content)...)
		default:
			return nil, fmt.Errorf("unsupported message role: %s", msg.Role)
		}
	}

	return parts, nil
}

// convertTools converts tools to Vertex AI format
func (c *Client) convertTools(tools []interfaces.Tool) []*genai.Tool {
	var vertexTools []*genai.Tool

	for _, tool := range tools {
		schema := &genai.Schema{
			Type: genai.TypeObject,
		}

		// Get tool parameters
		parameters := tool.Parameters()
		if len(parameters) > 0 {
			schema.Properties = make(map[string]*genai.Schema)

			for name, param := range parameters {
				propSchema := &genai.Schema{
					Description: param.Description,
				}

				switch param.Type {
				case "string":
					propSchema.Type = genai.TypeString
				case "number":
					propSchema.Type = genai.TypeNumber
				case "boolean":
					propSchema.Type = genai.TypeBoolean
				case "array":
					propSchema.Type = genai.TypeArray
				case "object":
					propSchema.Type = genai.TypeObject
				default:
					propSchema.Type = genai.TypeString
				}

				schema.Properties[name] = propSchema

				if param.Required {
					schema.Required = append(schema.Required, name)
				}
			}
		}

		vertexTool := &genai.Tool{
			FunctionDeclarations: []*genai.FunctionDeclaration{
				{
					Name:        tool.Name(),
					Description: tool.Description(),
					Parameters:  schema,
				},
			},
		}

		vertexTools = append(vertexTools, vertexTool)
	}

	return vertexTools
}

func (c *Client) convertResponseFormat(format *interfaces.ResponseFormat) *genai.Schema {
	var vertexFormat *genai.Schema
	vertexFormat = &genai.Schema{
		Type: genai.TypeString,
	}
	if format.Type == interfaces.ResponseFormatJSON {
		vertexFormat.Type = genai.TypeObject
		vertexFormat.Properties = interfaces.ConvertJSONSchema(format.Schema)
	}
}

// getReasoningInstruction returns the reasoning instruction based on the mode
func (c *Client) getReasoningInstruction() string {
	switch c.reasoningMode {
	case ReasoningModeMinimal:
		return "Provide clear, direct responses with brief explanations when necessary."
	case ReasoningModeComprehensive:
		return "Think through problems step by step, showing your reasoning process and providing detailed explanations."
	default:
		return ""
	}
}

// withRetry executes the function with exponential backoff retry logic
func (c *Client) withRetry(ctx context.Context, fn func() error) error {
	exponentialBackoff := backoff.NewExponentialBackOff()
	exponentialBackoff.InitialInterval = c.retryDelay
	exponentialBackoff.MaxElapsedTime = time.Duration(c.maxRetries) * c.retryDelay * 2

	return backoff.Retry(fn, backoff.WithContext(exponentialBackoff, ctx))
}

// Close closes the Vertex AI client
func (c *Client) Close() error {
	return nil
}
